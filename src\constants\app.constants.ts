export enum RecordingState {
  STOPPED = 'stopped',
  RECORDING = 'recording',
  PAUSED = 'paused',
  ERROR = "error"
}
export enum Keyword {
  OPEN_SP_BEFORE = 'da kai dui hua',
  OPEN_SP_AFTER = 'da kai bing li',
  OPEN_LAB_BEFORE = 'da kai shi yan shi kao shi',
  OPEN_LAB_AFTER = 'da kai shi yan shi ping fen',
  CLOSE_SP_BEFORE = 'guan bi dui hua',
  CLOSE_SP_AFTER = 'guan bi bing li',
  CLOSE_LAB_BEFORE = 'guan bi shi yan shi kao shi',
  CLOSE_LAB_AFTER = 'guan bi shi yan shi ping fen',
}
