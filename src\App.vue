<script setup lang="ts">
import { onMounted, onUnmounted, ref, computed } from 'vue'
import { useRecorder } from '@/hooks/useRecorder'
import { useFunasr } from '@/hooks/useFunasr'
interface MessageData {
  text: string
  isFinish: boolean
}
const { audioState, recOpen, recStop } = useRecorder({
  onAudioProcess: (chunk: Int16Array) => audioProcess(chunk),
})
const { wsStart, sendChunk, state, endAudioProcessing } = useFunasr(
  'ws://*************:10096/',
  (message: MessageData) => messageHandle(message),
)
// const { wsStart, sendChunk, endAudioProcessing } = useFunasr('ws://*************:10096/', (text) =>
//   messageHandle(text),
// )

const text = ref('')
const isRecording = computed(() => {
  return state.value === 'start' && audioState.value === 'recording'
})
// #region 录音和音频处理
const audioProcess = (chunk: Int16Array) => {
  sendChunk(chunk)
}
const openRecording = () => {
  text.value = ''
  recOpen()
  wsStart()
}
const closeRecording = () => {
  recStop()
  endAudioProcessing()
}
// #endregion

// #region 识别结果处理
const messageHandle = (message: MessageData) => {
  console.log('识别结果:', message.text)
  text.value = message.text
  // const cleanText = text.replace(/[.,。，！？!?]/g, '')
}

// #endregion

// onMounted(() => {
//   openRecording()
// })
onUnmounted(() => {
  closeRecording()
})
</script>
<template>
  <div class="home-page">
    <div class="text-box">{{ text }}</div>
    <div class="button-box">
      <div
        :class="[isRecording ? 'over' : 'start']"
        @click="isRecording ? closeRecording() : openRecording()"
      >
        {{ isRecording ? '结束录制' : '开始录制' }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.text-box {
  width: 100%;
  height: 400px;
  padding: 20px;
  border: 1px solid #fff;
  font-size: 16px;
  color: #fff;
  overflow: auto;
}

.button-box {
  display: flex;
  align-items: center;
  margin-top: 10px;
  .over {
    width: 80px;
    height: 40px;
    background: #ff6363;
    color: #fff;
    text-align: center;
    line-height: 40px;
    border-radius: 10px;
    cursor: pointer;
  }
  .start {
    width: 80px;
    height: 40px;
    margin-left: 5px;
    background: #7d7fff;
    color: #fff;
    text-align: center;
    line-height: 40px;
    border-radius: 10px;
    cursor: pointer;
  }
}
</style>
