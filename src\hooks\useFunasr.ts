import { ref } from 'vue'
interface MessageData {
  text: string
  isFinish: boolean
}
export function useFunasr(url: string, onMessageHandle: (message: MessageData) => void) {
  let reconnectAttempts = 0
  const MAX_RECONNECT_ATTEMPTS = 5
  const RECONNECT_DELAY = 3000 // 3秒后重连
  const state = ref('stop')
  let temporaryText = ''
  let confirmedText = '' // 已确认的文字（不会被覆盖）
  const hotwords = JSON.stringify({

  })
  const speechSocket = ref<WebSocket | null>(null)

  function wsStart() {
    if ('WebSocket' in window) {
      connect()
      return 1
    } else {
      alert('当前浏览器不支持 WebSocket')
      return 0
    }
  }

  function connect() {
    speechSocket.value = new WebSocket(url)
    speechSocket.value.onopen = onOpen
    speechSocket.value.onclose = onClose
    speechSocket.value.onmessage = onMessage
    speechSocket.value.onerror = onError
  }

  function reconnect() {
    if (reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++
      console.log(`尝试重连... (${reconnectAttempts}/${MAX_RECONNECT_ATTEMPTS})`)
      setTimeout(() => {
        connect()
      }, RECONNECT_DELAY)
    } else {
      console.log('重连次数已达到最大值，停止重连')
    }
  }

  // SOCEKT连接中的消息与状态响应
  function onOpen() {
    reconnectAttempts = 0
    state.value = 'start'
    // 初始配置
    const request = {
      chunk_size: [5, 10, 5],
      // hotwords: hotwords,
      wav_name: 'h5',
      is_speaking: true,
      chunk_interval: 10,
      itn: true,
      mode: '2pass-offline',
      audio_fs: 16000,
      audio_format: 'wav',
    }

    console.log('发送初始配置:', JSON.stringify(request, null, 2))
    speechSocket.value?.send(JSON.stringify(request))
    console.log('连接成功')
  }
  function onClose(e: CloseEvent) {
    console.log('连接关闭!', e)
    state.value = 'stop'

    // 非主动关闭的情况下，尝试重连
    if (e.code !== 1000) {
      reconnect()
    }
  }
  function onMessage(e: MessageEvent) {
    try {
      //   if (useRecorder().isRecording.value === '3') {
      //     return
      //   }

      const data = JSON.parse(e.data)
      // 处理语音识别结果
      if (data.stamp_sents) {
        // 校验完成的最终文本 - 将当前临时文本确认并追加到已确认文本
        confirmedText += data.text
        temporaryText = '' // 清空临时文本

        const messageData = {
          text: confirmedText, // 发送完整的已确认文本
          isFinish: data.is_final
        }
        onMessageHandle(messageData)

      } else {
        // 实时识别的临时文本
        temporaryText += data.text

        const messageData = {
          text: confirmedText + temporaryText, // 已确认文本 + 当前临时文本
          isFinish: data.is_final
        }
        onMessageHandle(messageData)

      }
    } catch (error) {
      console.error('解析消息失败：', error)
    }
  }
  function onError(e: Event) {
    console.log('连接错误!', e)
    state.value = 'stop'
    // useSocketStore().stateHandle(2)
    // 发生错误时尝试重连
    reconnect()
  }

  function sendChunk(chunk: Int16Array) {
    if (speechSocket.value && speechSocket.value.readyState === WebSocket.OPEN) {
      speechSocket.value.send(chunk)
    }
  }
  function endAudioProcessing() {
    const chunk_size = [5, 10, 5]
    const request = {
      chunk_size,
      wav_name: 'h5',
      is_speaking: false,
      chunk_interval: 10,
      mode: '2pass-offline',
    }
    speechSocket.value?.send(JSON.stringify(request))
  }

  // 重置识别状态
  function resetRecognition() {
    // 如果连接已打开
    if (speechSocket.value && speechSocket.value.readyState === 1) {
      // 发送结束当前识别会话的信号
      const endRequest = {
        is_speaking: false,
      }
      speechSocket.value.send(JSON.stringify(endRequest))
      console.log('发送结束当前识别会话信号')

      // 短暂延迟后开始新的识别会话
      setTimeout(() => {
        // 开始新的识别会话
        const startRequest = {
          chunk_size: [5, 10, 5],
          hotwords: hotwords,
          wav_name: 'h5',
          is_speaking: true,
          chunk_interval: 10,
          itn: true,
          mode: '2pass-offline',
          audio_fs: 16000,
          audio_format: 'wav',
        }
        speechSocket.value?.send(JSON.stringify(startRequest))
        console.log('发送开始新识别会话信号')
      }, 300) // 300ms延迟，确保结束信号被处理

      return true
    }
    return false
  }

  return { wsStart, sendChunk, state, endAudioProcessing, resetRecognition }
}
